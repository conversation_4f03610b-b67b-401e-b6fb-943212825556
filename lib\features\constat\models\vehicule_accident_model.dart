import 'package:cloud_firestore/cloud_firestore.dart';

class VehiculeAccidentModel {
  final String? id;
  final String marque;
  final String type;
  final String numeroImmatriculation;
  final String? venantDe;
  final String? allantA;
  final String? sensCirculation; // Ajout du champ manquant
  final List<String> degatsApparents;
  final String? photoCarteGriseUrl;
  final String conducteurId; // Lien avec le conducteur
  final DateTime createdAt;
  final DateTime? updatedAt;
  
  VehiculeAccidentModel({
    this.id,
    required this.marque,
    required this.type,
    required this.numeroImmatriculation,
    this.venantDe,
    this.allantA,
    this.sensCirculation, // Ajout du paramètre
    this.degatsApparents = const [],
    this.photoCarteGriseUrl,
    required this.conducteurId,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'marque': marque,
      'type': type,
      'numeroImmatriculation': numeroImmatriculation,
      'venantDe': venantDe,
      'allantA': allantA,
      'sensCirculation': sensCirculation, // Ajout dans toMap
      'degatsApparents': degatsApparents,
      'photoCarteGriseUrl': photoCarteGriseUrl,
      'conducteurId': conducteurId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'marque': marque,
      'type': type,
      'numeroImmatriculation': numeroImmatriculation,
      'venantDe': venantDe,
      'allantA': allantA,
      'sensCirculation': sensCirculation, // Ajout dans toJson
      'degatsApparents': degatsApparents,
      'photoCarteGriseUrl': photoCarteGriseUrl,
      'conducteurId': conducteurId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  static VehiculeAccidentModel fromMap(Map<String, dynamic> map) {
    return VehiculeAccidentModel(
      id: map['id'],
      marque: map['marque'] ?? '',
      type: map['type'] ?? '',
      numeroImmatriculation: map['numeroImmatriculation'] ?? '',
      venantDe: map['venantDe'],
      allantA: map['allantA'],
      sensCirculation: map['sensCirculation'], // Ajout dans fromMap
      degatsApparents: map['degatsApparents'] != null 
          ? List<String>.from(map['degatsApparents']) : [],
      photoCarteGriseUrl: map['photoCarteGriseUrl'],
      conducteurId: map['conducteurId'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: map['updatedAt'] != null 
          ? (map['updatedAt'] as Timestamp).toDate() : null,
    );
  }

  factory VehiculeAccidentModel.fromJson(Map<String, dynamic> json) {
    return VehiculeAccidentModel(
      id: json['id'],
      marque: json['marque'] ?? '',
      type: json['type'] ?? '',
      numeroImmatriculation: json['numeroImmatriculation'] ?? '',
      venantDe: json['venantDe'],
      allantA: json['allantA'],
      sensCirculation: json['sensCirculation'], // Ajout dans fromJson
      degatsApparents: json['degatsApparents'] != null 
          ? List<String>.from(json['degatsApparents']) : [],
      photoCarteGriseUrl: json['photoCarteGriseUrl'],
      conducteurId: json['conducteurId'] ?? '',
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  VehiculeAccidentModel copyWith({
    String? id,
    String? marque,
    String? type,
    String? numeroImmatriculation,
    String? venantDe,
    String? allantA,
    String? sensCirculation, // Ajout dans copyWith
    List<String>? degatsApparents,
    String? photoCarteGriseUrl,
    String? conducteurId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VehiculeAccidentModel(
      id: id ?? this.id,
      marque: marque ?? this.marque,
      type: type ?? this.type,
      numeroImmatriculation: numeroImmatriculation ?? this.numeroImmatriculation,
      venantDe: venantDe ?? this.venantDe,
      allantA: allantA ?? this.allantA,
      sensCirculation: sensCirculation ?? this.sensCirculation, // Ajout dans le retour
      degatsApparents: degatsApparents ?? this.degatsApparents,
      photoCarteGriseUrl: photoCarteGriseUrl ?? this.photoCarteGriseUrl,
      conducteurId: conducteurId ?? this.conducteurId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}