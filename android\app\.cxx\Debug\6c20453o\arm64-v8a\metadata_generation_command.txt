                        -HC:\flutter_windows_3.29.1-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=D:\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\PFE\constat_tunisie\build\app\intermediates\cxx\Debug\6c20453o\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\PFE\constat_tunisie\build\app\intermediates\cxx\Debug\6c20453o\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BC:\Users\<USER>\PFE\constat_tunisie\android\app\.cxx\Debug\6c20453o\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2