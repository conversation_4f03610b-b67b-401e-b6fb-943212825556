import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod

import 'core/config/app_routes.dart';
import 'core/config/app_theme.dart';
import 'core/services/notification_reminder_service.dart';
// Ensure these provider files export Riverpod providers, not just classes.
// For example, in auth_provider.dart:
// final authProvider = ChangeNotifierProvider((ref) => AuthProvider());
// import 'features/auth/providers/auth_provider.dart';
// import 'features/vehicule/providers/vehicule_provider.dart';
// import 'features/conducteur/providers/conducteur_provider.dart';
// import 'features/constat/providers/session_provider.dart';
// ConstatProvider is already a Riverpod provider, so no need to list it here for setup.

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  if (kDebugMode) {
    debugPrint('[CONSTAT_APP] Starting application in debug mode');
  }
  
  try {
    await Firebase.initializeApp();
    debugPrint('[CONSTAT_APP] Firebase initialized successfully');
    
    try {
      await NotificationReminderService().initialize();
      debugPrint('[CONSTAT_APP] Notification reminder service initialized successfully');
    } catch (e) {
      debugPrint('[CONSTAT_APP] Warning: Could not initialize notification service: $e');
    }
  } catch (e) {
    debugPrint('[CONSTAT_APP] Error during initialization: $e');
  }
  
  // Wrap the app with ProviderScope for Riverpod
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // No need for MultiProvider if all providers are Riverpod providers
    // defined globally and accessed via ref or Consumer.
    return MaterialApp(
      title: 'Constat Tunisie',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.lightTheme, // Or AppTheme.darkTheme if you have one
      themeMode: ThemeMode.light,
      debugShowCheckedModeBanner: false,
      navigatorObservers: [
        _NavigationObserver(),
      ],
      initialRoute: AppRoutes.splash,
      routes: AppRoutes.routes,
      onGenerateRoute: AppRoutes.generateRoute,
    );
  }
}

class _NavigationObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint('[CONSTAT_APP] Navigation: Pushed ${route.settings.name} (from ${previousRoute?.settings.name})');
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint('[CONSTAT_APP] Navigation: Popped ${route.settings.name} (back to ${previousRoute?.settings.name})');
    super.didPop(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    debugPrint('[CONSTAT_APP] Navigation: Replaced ${oldRoute?.settings.name} with ${newRoute?.settings.name}');
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    debugPrint('[CONSTAT_APP] Navigation: Removed ${route.settings.name}');
    super.didRemove(route, previousRoute);
  }
}