{"buildFiles": ["C:\\flutter_windows_3.29.1-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\PFE\\constat_tunisie\\android\\app\\.cxx\\Debug\\6c20453o\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\PFE\\constat_tunisie\\android\\app\\.cxx\\Debug\\6c20453o\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}