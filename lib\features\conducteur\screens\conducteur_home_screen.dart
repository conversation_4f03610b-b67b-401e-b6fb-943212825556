import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Change to Riverpod
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';

import '../../../core/config/app_routes.dart';
import '../../../core/widgets/custom_app_bar.dart'; 
import '../../../features/auth/providers/auth_provider.dart';
import '../../../features/auth/models/user_model.dart'; 

class ConducteurHomeScreen extends ConsumerStatefulWidget { // Change to ConsumerStatefulWidget
  const ConducteurHomeScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ConducteurHomeScreen> createState() => _ConducteurHomeScreenState(); // Change to ConsumerState
}

class _ConducteurHomeScreenState extends ConsumerState<ConducteurHomeScreen> { // Change to ConsumerState
  @override
  void initState() {
    super.initState();
    initializeDateFormatting('fr_FR', null);
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider); // Use Riverpod ref.watch instead of Provider.of
    final UserModel? user = authState.currentUser; 
    
    final dateFormat = DateFormat.yMMMMd('fr_FR');
    final today = dateFormat.format(DateTime.now());
    
    return Scaffold(
      appBar: CustomAppBar( 
        title: 'Tableau de bord',
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
               ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Écran de notifications à implémenter')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await authState.signOut(); // Use authState instead of authProvider
              if (context.mounted) {
                Navigator.pushNamedAndRemoveUntil(context, AppRoutes.login, (route) => false);
              }
            },
          ),
        ],
      ),
      // Rest of the code remains the same
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    child: Text(
                      (user?.prenom.isNotEmpty == true && user?.nom.isNotEmpty == true)
                          ? '${user!.prenom[0]}${user.nom[0]}'.toUpperCase()
                          : 'U',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Bonjour, ${user?.prenom ?? ''} ${user?.nom ?? ''}',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        Text(
                          today,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
              
              InkWell(
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.declarationEntryPoint); // MODIFIÉ
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.primaryContainer],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withAlpha(77),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.add_circle_outline, color: Colors.white, size: 32),
                      SizedBox(height: 16),
                      Text('Nouveau constat', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white)),
                      SizedBox(height: 8),
                      Text('Déclarer un accident et créer un constat amiable', style: TextStyle(fontSize: 14, color: Colors.white70)),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              _buildQuickActions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Accès rapide', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildFeatureCard(context, 'Mes véhicules', Icons.directions_car, AppRoutes.conducteurVehicules),
            _buildFeatureCard(context, 'Historique Constats', Icons.history, AppRoutes.conducteurAccidents),
            _buildFeatureCard(context, 'Mon Profil', Icons.person_outline, AppRoutes.conducteurProfile),
            _buildFeatureCard(context, 'Paramètres', Icons.settings_outlined, '/settings_placeholder'),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureCard(BuildContext context, String title, IconData icon, String route) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          if (route == AppRoutes.conducteurVehicules) {
             Navigator.pushNamed(context, route, arguments: {'selectionMode': false}); // Explicitement false
          } else if (route == '/settings_placeholder') {
            ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Écran Paramètres à implémenter')));
          }
          else {
            Navigator.pushNamed(context, route);
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(icon, size: 40, color: Theme.of(context).colorScheme.primary),
            const SizedBox(height: 10),
            Text(title, textAlign: TextAlign.center, style: Theme.of(context).textTheme.titleSmall),
          ],
        ),
      ),
    );
  }
}