import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../vehicule/models/vehicule_model.dart';
import '../../vehicule/providers/vehicule_provider.dart'; 
import '../widgets/vehicule_card.dart'; 
import '../../constat/screens/conducteur_declaration_screen.dart'; 
import '../../../core/config/app_routes.dart';

class ConducteurVehiculesScreen extends StatefulWidget {
  final String conducteurPosition; 
  final String? sessionId; 
  final bool isCollaborative;

  const ConducteurVehiculesScreen({
    Key? key,
    required this.conducteurPosition,
    this.sessionId,
    this.isCollaborative = false,
  }) : super(key: key);

  @override
  State<ConducteurVehiculesScreen> createState() => _ConducteurVehiculesScreenState();
}

class _ConducteurVehiculesScreenState extends State<ConducteurVehiculesScreen> {
  @override
  void initState() {
    super.initState();
    // CORRECTION: Passage à Provider pour récupérer les données
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<VehiculeProvider>(context, listen: false).fetchVehicules();
    });
  }

  void _onVehiculeSelected(VehiculeModel vehicule) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ConducteurDeclarationScreen(
          conducteurPosition: widget.conducteurPosition,
          sessionId: widget.sessionId,
          isCollaborative: widget.isCollaborative,
          selectedVehicule: vehicule,
        ),
      ),
    );
  }

  void _skipVehiculeSelection() {
     Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ConducteurDeclarationScreen(
          conducteurPosition: widget.conducteurPosition,
          sessionId: widget.sessionId,
          isCollaborative: widget.isCollaborative,
          selectedVehicule: null, 
        ),
      ),
    );
  }

  Future<void> _refreshVehicules() async {
    await Provider.of<VehiculeProvider>(context, listen: false).fetchVehicules();
  }

  @override
  Widget build(BuildContext context) {
    final Color appBarColor = widget.conducteurPosition == 'A' ? Colors.blueAccent : Colors.greenAccent;

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Sélectionner un Véhicule',
        backgroundColor: appBarColor,
      ),
      body: RefreshIndicator(
        onRefresh: _refreshVehicules,
        child: Consumer<VehiculeProvider>(
          builder: (context, vehiculeProvider, child) {
            return _buildBody(vehiculeProvider, appBarColor);
          },
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, AppRoutes.addVehicule);
        },
        label: const Text('Ajouter Véhicule'),
        icon: const Icon(Icons.add),
        backgroundColor: appBarColor,
      ),
    );
  }

  Widget _buildBody(VehiculeProvider vehiculeProvider, Color cardAccentColor) { 
    if (vehiculeProvider.isLoading && vehiculeProvider.vehicules.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (vehiculeProvider.error != null && vehiculeProvider.vehicules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text('Erreur: ${vehiculeProvider.error}. Veuillez réessayer.'),
            const SizedBox(height: 16),
            CustomButton(
              text: 'Réessayer',
              onPressed: _refreshVehicules,
              color: cardAccentColor,
            ),
          ],
        ),
      );
    }

    if (vehiculeProvider.vehicules.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.directions_car, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            const Text('Aucun véhicule trouvé. Veuillez en ajouter un pour continuer ou passer cette étape.'),
            const SizedBox(height: 16),
            CustomButton(
              text: 'Ajouter un véhicule',
              onPressed: () => Navigator.pushNamed(context, AppRoutes.addVehicule),
              color: cardAccentColor,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        if (vehiculeProvider.isLoading)
          const Padding(
            padding: EdgeInsets.all(8.0),
            child: Center(child: SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 3))),
          ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: vehiculeProvider.vehicules.length,
            itemBuilder: (context, index) {
              final vehicule = vehiculeProvider.vehicules[index];
              return VehiculeCard(
                vehicule: vehicule,
                onTap: () => _onVehiculeSelected(vehicule),
                cardColor: cardAccentColor.withAlpha(25),
                accentColor: cardAccentColor,
              );
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
          child: CustomButton(
            text: 'Continuer sans sélectionner de véhicule',
            onPressed: _skipVehiculeSelection,
            color: Colors.grey,
            isOutlined: true,
          ),
        ),
      ],
    );
  }
}