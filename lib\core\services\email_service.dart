import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';

class EmailService {
  // Configuration pour Mailtrap (gratuit pour les tests)
  static const String _mailtrapToken = 'YOUR_MAILTRAP_TOKEN'; // Remplacez par votre token Mailtrap
  static const String _baseUrl = 'https://send.api.mailtrap.io/api/send';
  static const String _fromEmail = '<EMAIL>'; // Votre email d'expéditeur
  static const String _appUrl = 'https://constat-tunisie.com'; // URL de votre app web

  // Pour les tests, utilisez ces valeurs
  static const String _testFromEmail = '<EMAIL>';
  static const String _testAppUrl = 'http://localhost:3000'; // URL de test

  Future<void> sendEmailWithAttachment({
    required String to,
    required String subject,
    required String body,
    required String attachmentPath,
  }) async {
    // Implementation avec SendGrid ou autre service
    debugPrint('EmailService: Sending email with attachment to $to, subject: $subject, attachment: $attachmentPath');
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> sendEmail({
    required String to,
    required String subject,
    required String body,
  }) async {
    try {
      debugPrint('[EmailService] Envoi email à: $to');

      if (kDebugMode) {
        // En mode debug, on simule l'envoi
        debugPrint('[EmailService] MODE DEBUG - Email simulé');
        debugPrint('[EmailService] To: $to');
        debugPrint('[EmailService] Subject: $subject');
        debugPrint('[EmailService] Body: $body');
        await Future.delayed(const Duration(milliseconds: 1000));
        return;
      }

      // Envoi réel avec Mailtrap
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_mailtrapToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'from': {
            'email': _fromEmail,
            'name': 'Constat Tunisie',
          },
          'to': [
            {
              'email': to,
            }
          ],
          'subject': subject,
          'html': body,
        }),
      );

      if (response.statusCode == 202) {
        debugPrint('[EmailService] Email envoyé avec succès');
      } else {
        throw Exception('Erreur envoi email: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('[EmailService] Erreur: $e');
      rethrow;
    }
  }

  Future<void> envoyerInvitation({
    required String email,
    required String sessionCode,
    required String sessionId,
  }) async {
    try {
      debugPrint('[EmailService] === ENVOI INVITATION ===');
      debugPrint('[EmailService] Destinataire: $email');
      debugPrint('[EmailService] Code session: $sessionCode');
      debugPrint('[EmailService] ID session: $sessionId');

      // Créer le lien d'invitation
      final invitationLink = '$_appUrl/join-session?code=$sessionCode&id=$sessionId';

      // Créer le contenu HTML de l'email
      final htmlBody = _createInvitationEmailBody(sessionCode, invitationLink);

      if (kDebugMode) {
        // En mode debug, afficher le contenu de l'email et ouvrir l'app email
        debugPrint('[EmailService] === CONTENU EMAIL ===');
        debugPrint('[EmailService] Sujet: Invitation - Constat d\'accident collaboratif');
        debugPrint('[EmailService] Destinataire: $email');
        debugPrint('[EmailService] Code de session: $sessionCode');
        debugPrint('[EmailService] Lien: $invitationLink');
        debugPrint('[EmailService] === FIN CONTENU EMAIL ===');

        // Ouvrir l'application email par défaut avec le contenu pré-rempli
        await _openEmailApp(email, sessionCode, invitationLink);
      } else {
        // En production, utiliser le vrai service d'email
        await sendEmail(
          to: email,
          subject: 'Invitation - Constat d\'accident collaboratif',
          body: htmlBody,
        );
      }

      debugPrint('[EmailService] Invitation envoyée avec succès à: $email');
    } catch (e) {
      debugPrint('[EmailService] Erreur envoi invitation: $e');
      rethrow;
    }
  }

  Future<void> _openEmailApp(String email, String sessionCode, String invitationLink) async {
    try {
      final subject = Uri.encodeComponent('Invitation - Constat d\'accident collaboratif');
      final body = Uri.encodeComponent('''
Bonjour,

Vous avez été invité(e) à participer à un constat d'accident collaboratif.

Code de session: $sessionCode

Pour rejoindre la session:
1. Ouvrez l'application Constat Tunisie
2. Appuyez sur "Rejoindre une session"
3. Saisissez le code: $sessionCode

Ou utilisez ce lien: $invitationLink

Cette invitation expire dans 24 heures.

Cordialement,
L'équipe Constat Tunisie
      ''');

      final mailtoUrl = 'mailto:$email?subject=$subject&body=$body';
      final uri = Uri.parse(mailtoUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        debugPrint('[EmailService] Application email ouverte');
      } else {
        debugPrint('[EmailService] Impossible d\'ouvrir l\'application email');
      }
    } catch (e) {
      debugPrint('[EmailService] Erreur ouverture app email: $e');
    }
  }

  String _createInvitationEmailBody(String sessionCode, String invitationLink) {
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Invitation Constat Collaboratif</title>
    </head>
    <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 10px;">
            <h2 style="color: #2563eb; text-align: center;">🚗 Constat d'Accident Collaboratif</h2>

            <p>Bonjour,</p>

            <p>Vous avez été invité(e) à participer à un constat d'accident collaboratif.</p>

            <div style="background-color: white; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center;">
                <h3 style="color: #1f2937; margin-top: 0;">Code de session:</h3>
                <div style="font-size: 24px; font-weight: bold; color: #2563eb; letter-spacing: 2px;">$sessionCode</div>
            </div>

            <p><strong>Comment rejoindre la session :</strong></p>
            <ol>
                <li>Cliquez sur le bouton ci-dessous ou utilisez le code de session</li>
                <li>Connectez-vous à votre compte ou créez-en un</li>
                <li>Remplissez vos informations dans le constat</li>
            </ol>

            <div style="text-align: center; margin: 30px 0;">
                <a href="$invitationLink"
                   style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                   Rejoindre la Session
                </a>
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <p style="margin: 0; color: #92400e;"><strong>⚠️ Important :</strong></p>
                <ul style="color: #92400e; margin: 10px 0;">
                    <li>Cette invitation expire dans 24 heures</li>
                    <li>Assurez-vous d'avoir vos documents (permis, carte grise, attestation d'assurance)</li>
                    <li>Prenez des photos de l'accident si ce n'est pas déjà fait</li>
                </ul>
            </div>

            <p style="color: #6b7280; font-size: 14px; text-align: center; margin-top: 30px;">
                Cet email a été envoyé automatiquement par l'application Constat Tunisie.<br>
                Si vous n'êtes pas concerné par cet accident, ignorez cet email.
            </p>
        </div>
    </body>
    </html>
    ''';
  }
}
