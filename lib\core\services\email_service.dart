import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';

class EmailService {
  // Configuration EmailJS (gratuit - 200 emails/mois)
  // 🔧 REMPLACEZ CES VALEURS PAR VOS VRAIES VALEURS EMAILJS :
  // Exemple : static const String _emailjsServiceId = 'service_abc123';
  static const String _emailjsServiceId = 'VOTRE_SERVICE_ID'; // ← Remplacez ici
  static const String _emailjsTemplateId = 'VOTRE_TEMPLATE_ID'; // ← Remplacez ici
  static const String _emailjsPublicKey = 'VOTRE_PUBLIC_KEY'; // ← Remplacez ici
  static const String _emailjsUrl = 'https://api.emailjs.com/api/v1.0/email/send';

  // Configuration alternative : Webhook de test (pour démonstration)
  static const String _webhookTestUrl = 'https://httpbin.org/post'; // Service de test

  // Configuration pour Mailtrap (alternative)
  static const String _mailtrapToken = 'YOUR_MAILTRAP_TOKEN'; // Remplacez par votre token Mailtrap
  static const String _baseUrl = 'https://send.api.mailtrap.io/api/send';
  static const String _fromEmail = '<EMAIL>'; // Votre email d'expéditeur
  static const String _appUrl = 'https://constat-tunisie.com'; // URL de votre app web

  // Pour les tests, utilisez ces valeurs
  static const String _testFromEmail = '<EMAIL>';
  static const String _testAppUrl = 'http://localhost:3000'; // URL de test

  Future<void> sendEmailWithAttachment({
    required String to,
    required String subject,
    required String body,
    required String attachmentPath,
  }) async {
    // Implementation avec SendGrid ou autre service
    debugPrint('EmailService: Sending email with attachment to $to, subject: $subject, attachment: $attachmentPath');
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> sendEmail({
    required String to,
    required String subject,
    required String body,
  }) async {
    try {
      debugPrint('[EmailService] Envoi email à: $to');

      if (kDebugMode) {
        // En mode debug, on simule l'envoi
        debugPrint('[EmailService] MODE DEBUG - Email simulé');
        debugPrint('[EmailService] To: $to');
        debugPrint('[EmailService] Subject: $subject');
        debugPrint('[EmailService] Body: $body');
        await Future.delayed(const Duration(milliseconds: 1000));
        return;
      }

      // Envoi réel avec Mailtrap
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_mailtrapToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'from': {
            'email': _fromEmail,
            'name': 'Constat Tunisie',
          },
          'to': [
            {
              'email': to,
            }
          ],
          'subject': subject,
          'html': body,
        }),
      );

      if (response.statusCode == 202) {
        debugPrint('[EmailService] Email envoyé avec succès');
      } else {
        throw Exception('Erreur envoi email: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('[EmailService] Erreur: $e');
      rethrow;
    }
  }

  Future<void> envoyerInvitation({
    required String email,
    required String sessionCode,
    required String sessionId,
  }) async {
    try {
      debugPrint('[EmailService] === ENVOI INVITATION ===');
      debugPrint('[EmailService] Destinataire: $email');
      debugPrint('[EmailService] Code session: $sessionCode');
      debugPrint('[EmailService] ID session: $sessionId');

      // Créer le lien d'invitation
      final invitationLink = '$_appUrl/join-session?code=$sessionCode&id=$sessionId';



      // Afficher le contenu de l'email dans les logs
      debugPrint('[EmailService] === CONTENU EMAIL ===');
      debugPrint('[EmailService] Sujet: Invitation - Constat d\'accident collaboratif');
      debugPrint('[EmailService] Destinataire: $email');
      debugPrint('[EmailService] Code de session: $sessionCode');
      debugPrint('[EmailService] Lien: $invitationLink');
      debugPrint('[EmailService] === FIN CONTENU EMAIL ===');

      // Essayer d'abord l'envoi automatique, puis fallback vers l'app email
      bool emailSent = false;

      // Tentative d'envoi automatique avec EmailJS
      if (_emailjsServiceId != 'VOTRE_SERVICE_ID' &&
          _emailjsTemplateId != 'VOTRE_TEMPLATE_ID' &&
          _emailjsPublicKey != 'VOTRE_PUBLIC_KEY' &&
          _emailjsServiceId.startsWith('service_') &&
          _emailjsTemplateId.startsWith('template_') &&
          _emailjsPublicKey.startsWith('user_')) {
        debugPrint('[EmailService] 🚀 Configuration EmailJS détectée - Envoi automatique');
        emailSent = await _sendWithEmailJS(email, sessionCode, invitationLink);
      } else {
        debugPrint('[EmailService] ⚠️ EmailJS non configuré - Mode manuel activé');
        debugPrint('[EmailService] 📱 Ouverture de Gmail pour envoi manuel');
        // Forcer l'ouverture de l'app email au lieu de la simulation
        emailSent = false; // Force le fallback vers l'app email
      }

      if (!emailSent) {
        // Fallback : ouvrir l'app email pour envoi manuel
        debugPrint('[EmailService] 📱 Fallback: Ouverture de l\'application email...');
        await _openEmailApp(email, sessionCode, invitationLink);
        debugPrint('[EmailService] ✅ IMPORTANT: Appuyez sur ENVOYER dans votre app email!');
      }

      debugPrint('[EmailService] 📧 Email traité pour: $email');

      debugPrint('[EmailService] Invitation envoyée avec succès à: $email');
    } catch (e) {
      debugPrint('[EmailService] Erreur envoi invitation: $e');
      rethrow;
    }
  }



  Future<bool> _sendWithTestWebhook(String email, String sessionCode, String invitationLink) async {
    try {
      debugPrint('[EmailService] 🧪 Mode démonstration - Test webhook...');

      final response = await http.post(
        Uri.parse(_webhookTestUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'to': email,
          'subject': 'Invitation - Constat d\'accident collaboratif',
          'session_code': sessionCode,
          'invitation_link': invitationLink,
          'message': 'Email de test envoyé avec succès !',
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        debugPrint('[EmailService] ✅ Test webhook réussi ! (Simulation d\'envoi)');
        debugPrint('[EmailService] 📧 En production, l\'email serait envoyé à: $email');
        return true;
      } else {
        debugPrint('[EmailService] ❌ Test webhook échoué: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('[EmailService] ❌ Erreur test webhook: $e');
      return false;
    }
  }

  Future<bool> _sendWithEmailJS(String email, String sessionCode, String invitationLink) async {
    try {
      debugPrint('[EmailService] 🚀 Tentative d\'envoi automatique avec EmailJS...');

      final response = await http.post(
        Uri.parse(_emailjsUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'service_id': _emailjsServiceId,
          'template_id': _emailjsTemplateId,
          'user_id': _emailjsPublicKey,
          'template_params': {
            'to_email': email,
            'from_name': 'Constat Tunisie',
            'subject': 'Invitation - Constat d\'accident collaboratif',
            'session_code': sessionCode,
            'invitation_link': invitationLink,
            'message': '''
Bonjour,

Vous avez été invité(e) à participer à un constat d'accident collaboratif.

Code de session: $sessionCode

Pour rejoindre la session:
1. Ouvrez l'application Constat Tunisie
2. Appuyez sur "Rejoindre une session"
3. Saisissez le code: $sessionCode

Ou utilisez ce lien: $invitationLink

Cette invitation expire dans 24 heures.

Cordialement,
L'équipe Constat Tunisie
            ''',
          },
        }),
      );

      if (response.statusCode == 200) {
        debugPrint('[EmailService] ✅ Email envoyé automatiquement avec succès!');
        return true;
      } else {
        debugPrint('[EmailService] ❌ Échec EmailJS: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('[EmailService] ❌ Erreur EmailJS: $e');
      return false;
    }
  }

  Future<void> _openEmailApp(String email, String sessionCode, String invitationLink) async {
    try {
      final subject = Uri.encodeComponent('Invitation - Constat d\'accident collaboratif');
      final body = Uri.encodeComponent('''
Bonjour,

Vous avez été invité(e) à participer à un constat d'accident collaboratif.

Code de session: $sessionCode

Pour rejoindre la session:
1. Ouvrez l'application Constat Tunisie
2. Appuyez sur "Rejoindre une session"
3. Saisissez le code: $sessionCode

Ou utilisez ce lien: $invitationLink

Cette invitation expire dans 24 heures.

Cordialement,
L'équipe Constat Tunisie
      ''');

      final mailtoUrl = 'mailto:$email?subject=$subject&body=$body';
      final uri = Uri.parse(mailtoUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        debugPrint('[EmailService] Application email ouverte');
      } else {
        debugPrint('[EmailService] Impossible d\'ouvrir l\'application email');
      }
    } catch (e) {
      debugPrint('[EmailService] Erreur ouverture app email: $e');
    }
  }


}
