import 'package:flutter/foundation.dart';

class EmailService {
  Future<void> sendEmailWithAttachment({
    required String to,
    required String subject,
    required String body,
    required String attachmentPath,
  }) async {
    // Dummy implementation - Replace with your actual email sending logic (e.g., using a cloud function or third-party service)
    debugPrint('EmailService STUB: Sending email with attachment to $to, subject: $subject, attachment: $attachmentPath');
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> sendEmail({
    required String to,
    required String subject,
    required String body,
  }) async {
    // Dummy implementation
    debugPrint('EmailService STUB: Sending email to $to, subject: $subject');
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> envoyerInvitation({
    required String email,
    required String sessionCode,
    required String sessionId,
  }) async {
    // Dummy implementation
    debugPrint('EmailService STUB: Sending invitation to $email for session $sessionCode (ID: $sessionId)');
    await Future.delayed(const Duration(milliseconds: 500));
  }
}
