import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class EmailService {
  // Configuration pour un service d'email (exemple avec SendGrid)
  static const String _apiKey = 'YOUR_SENDGRID_API_KEY'; // Remplacez par votre vraie clé
  static const String _baseUrl = 'https://api.sendgrid.com/v3/mail/send';
  static const String _fromEmail = '<EMAIL>'; // Votre email d'expéditeur
  static const String _appUrl = 'https://constat-tunisie.com'; // URL de votre app web

  Future<void> sendEmailWithAttachment({
    required String to,
    required String subject,
    required String body,
    required String attachmentPath,
  }) async {
    // Implementation avec SendGrid ou autre service
    debugPrint('EmailService: Sending email with attachment to $to, subject: $subject, attachment: $attachmentPath');
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> sendEmail({
    required String to,
    required String subject,
    required String body,
  }) async {
    try {
      debugPrint('[EmailService] Envoi email à: $to');

      if (kDebugMode) {
        // En mode debug, on simule l'envoi
        debugPrint('[EmailService] MODE DEBUG - Email simulé');
        debugPrint('[EmailService] To: $to');
        debugPrint('[EmailService] Subject: $subject');
        debugPrint('[EmailService] Body: $body');
        await Future.delayed(const Duration(milliseconds: 1000));
        return;
      }

      // Envoi réel avec SendGrid
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'personalizations': [
            {
              'to': [{'email': to}],
              'subject': subject,
            }
          ],
          'from': {'email': _fromEmail, 'name': 'Constat Tunisie'},
          'content': [
            {
              'type': 'text/html',
              'value': body,
            }
          ],
        }),
      );

      if (response.statusCode == 202) {
        debugPrint('[EmailService] Email envoyé avec succès');
      } else {
        throw Exception('Erreur envoi email: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('[EmailService] Erreur: $e');
      rethrow;
    }
  }

  Future<void> envoyerInvitation({
    required String email,
    required String sessionCode,
    required String sessionId,
  }) async {
    try {
      debugPrint('[EmailService] Envoi invitation à: $email pour session: $sessionCode');

      // Créer le lien d'invitation
      final invitationLink = '$_appUrl/join-session?code=$sessionCode&id=$sessionId';

      // Créer le contenu HTML de l'email
      final htmlBody = _createInvitationEmailBody(sessionCode, invitationLink);

      await sendEmail(
        to: email,
        subject: 'Invitation - Constat d\'accident collaboratif',
        body: htmlBody,
      );

      debugPrint('[EmailService] Invitation envoyée avec succès à: $email');
    } catch (e) {
      debugPrint('[EmailService] Erreur envoi invitation: $e');
      rethrow;
    }
  }

  String _createInvitationEmailBody(String sessionCode, String invitationLink) {
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Invitation Constat Collaboratif</title>
    </head>
    <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 10px;">
            <h2 style="color: #2563eb; text-align: center;">🚗 Constat d'Accident Collaboratif</h2>

            <p>Bonjour,</p>

            <p>Vous avez été invité(e) à participer à un constat d'accident collaboratif.</p>

            <div style="background-color: white; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center;">
                <h3 style="color: #1f2937; margin-top: 0;">Code de session:</h3>
                <div style="font-size: 24px; font-weight: bold; color: #2563eb; letter-spacing: 2px;">$sessionCode</div>
            </div>

            <p><strong>Comment rejoindre la session :</strong></p>
            <ol>
                <li>Cliquez sur le bouton ci-dessous ou utilisez le code de session</li>
                <li>Connectez-vous à votre compte ou créez-en un</li>
                <li>Remplissez vos informations dans le constat</li>
            </ol>

            <div style="text-align: center; margin: 30px 0;">
                <a href="$invitationLink"
                   style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                   Rejoindre la Session
                </a>
            </div>

            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <p style="margin: 0; color: #92400e;"><strong>⚠️ Important :</strong></p>
                <ul style="color: #92400e; margin: 10px 0;">
                    <li>Cette invitation expire dans 24 heures</li>
                    <li>Assurez-vous d'avoir vos documents (permis, carte grise, attestation d'assurance)</li>
                    <li>Prenez des photos de l'accident si ce n'est pas déjà fait</li>
                </ul>
            </div>

            <p style="color: #6b7280; font-size: 14px; text-align: center; margin-top: 30px;">
                Cet email a été envoyé automatiquement par l'application Constat Tunisie.<br>
                Si vous n'êtes pas concerné par cet accident, ignorez cet email.
            </p>
        </div>
    </body>
    </html>
    ''';
  }
}
