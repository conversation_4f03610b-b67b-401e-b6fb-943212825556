# 📧 Guide de Configuration d'Envoi d'Emails Automatique

## 🚀 **Solution 2 : Activer l'Envoi Automatique (Recommandé)**

Pour que les emails soient envoyés automatiquement sans intervention manuelle, nous allons configurer un service d'email gratuit.

### **Option A : Configuration EmailJS (Recommandé - 200 emails/mois gratuits)**

#### **Étape 1 : Créer un compte EmailJS**

1. **Allez sur [emailjs.com](https://www.emailjs.com)**
2. **Créez un compte gratuit** avec votre email
3. **Confirmez votre email** via le lien reçu

#### **Étape 2 : Configurer un service email**

1. **Dans le dashboard EmailJS, cliquez sur "Add New Service"**
2. **Choisissez votre fournisseur email :**
   - Gmail (recommandé)
   - Outlook
   - Yahoo
   - Ou autre
3. **Suivez les instructions pour connecter votre compte**
4. **Notez votre `Service ID`** (ex: `service_abc123`)

#### **Étape 3 : <PERSON><PERSON>er un template d'email**

1. **Cliquez sur "Email Templates" puis "Create New Template"**
2. **Configurez le template :**
   ```
   Subject: {{subject}}
   
   To: {{to_email}}
   From: {{from_name}}
   
   Content:
   {{message}}
   
   Code de session: {{session_code}}
   Lien d'invitation: {{invitation_link}}
   ```
3. **Sauvegardez et notez votre `Template ID`** (ex: `template_xyz789`)

#### **Étape 4 : Obtenir votre clé publique**

1. **Allez dans "Account" > "General"**
2. **Notez votre `Public Key`** (ex: `user_def456`)

#### **Étape 5 : Configurer l'application**

Modifiez le fichier `lib/core/services/email_service.dart` :

```dart
class EmailService {
  // Configuration EmailJS - REMPLACEZ PAR VOS VRAIES VALEURS
  static const String _emailjsServiceId = 'service_abc123'; // Votre Service ID
  static const String _emailjsTemplateId = 'template_xyz789'; // Votre Template ID
  static const String _emailjsPublicKey = 'user_def456'; // Votre Public Key
  // ... reste du code
}
```

#### **Étape 6 : Tester**

1. **Relancez l'application**
2. **Cliquez sur "Test Email"**
3. **Entrez votre email**
4. **L'email devrait être envoyé automatiquement !**

### **Option B : Configuration Formspree (Alternative - 50 emails/mois gratuits)**

#### **Étape 1 : Créer un compte Formspree**

1. **Allez sur [formspree.io](https://formspree.io)**
2. **Créez un compte gratuit**
3. **Créez un nouveau formulaire**
4. **Notez votre Form ID** (ex: `xpzgkqyw`)

#### **Étape 2 : Modifier le service**

Remplacez la méthode `_sendWithEmailJS` par :

```dart
Future<bool> _sendWithFormspree(String email, String sessionCode, String invitationLink) async {
  try {
    final response = await http.post(
      Uri.parse('https://formspree.io/f/YOUR_FORM_ID'), // Remplacez YOUR_FORM_ID
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'email': email,
        'subject': 'Invitation - Constat d\'accident collaboratif',
        'message': '''
Bonjour,

Vous avez été invité(e) à participer à un constat d'accident collaboratif.

Code de session: $sessionCode

Pour rejoindre la session:
1. Ouvrez l'application Constat Tunisie
2. Appuyez sur "Rejoindre une session"
3. Saisissez le code: $sessionCode

Ou utilisez ce lien: $invitationLink

Cette invitation expire dans 24 heures.

Cordialement,
L'équipe Constat Tunisie
        ''',
      }),
    );
    
    return response.statusCode == 200;
  } catch (e) {
    debugPrint('[EmailService] Erreur Formspree: $e');
    return false;
  }
}
```

### **Option C : Configuration Mailtrap (Pour les tests)**

#### **Étape 1 : Créer un compte Mailtrap**

1. **Allez sur [mailtrap.io](https://mailtrap.io)**
2. **Créez un compte gratuit**
3. **Créez une inbox de test**
4. **Notez votre token API**

#### **Étape 2 : Configurer**

```dart
static const String _mailtrapToken = 'your_real_token_here';
```

## 🎯 **Résultat Final**

Avec EmailJS configuré :
- ✅ **Envoi automatique** d'emails réels
- ✅ **200 emails gratuits** par mois
- ✅ **Pas d'intervention manuelle** requise
- ✅ **Emails professionnels** avec template personnalisé

## 🔧 **Dépannage**

### **Problème : Emails non reçus**
- Vérifiez vos identifiants EmailJS
- Vérifiez le dossier spam
- Testez avec un autre email

### **Problème : Erreur 401/403**
- Vérifiez votre Public Key
- Vérifiez que le service est actif

### **Problème : Template non trouvé**
- Vérifiez le Template ID
- Assurez-vous que le template est publié

## 📱 **Test Complet**

1. **Configurez EmailJS** avec vos vraies valeurs
2. **Relancez l'application**
3. **Testez "Test Email"** - l'email arrive automatiquement
4. **Testez une session collaborative** - toutes les invitations sont envoyées
5. **Vérifiez vos emails** - vous devriez tout recevoir !

🎉 **Félicitations ! Votre système d'invitation par email est maintenant entièrement automatisé !**
