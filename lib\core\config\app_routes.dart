import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';

// Imports des écrans
import '../../features/splash/screens/splash_screen.dart';
import '../../features/onboarding/screens/onboarding_screen.dart';
import '../../features/language/screens/language_selection_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/conducteur/screens/conducteur_home_screen.dart';
import '../../features/conducteur/screens/conducteur_profile_screen.dart';
import '../../features/conducteur/screens/conducteur_vehicules_screen.dart';
import '../../features/conducteur/screens/conducteur_accidents_screen.dart';
import '../../features/constat/screens/conducteur_declaration_screen.dart';
import '../../features/assureur/screens/assureur_home_screen.dart';
import '../../features/expert/screens/expert_home_screen.dart';
import '../../features/constat/screens/declaration_entry_point_screen.dart';
import '../../features/constat/screens/session_creation_screen.dart';
import '../../features/vehicule/models/vehicule_model.dart';
import '../../features/vehicule/screens/vehicule_form_screen.dart';

class AppRoutes {
  // Routes principales
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String languageSelection = '/language-selection';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';

  // Routes conducteur
  static const String conducteurHome = '/conducteur/home';
  static const String conducteurProfile = '/conducteur/profile';
  static const String conducteurVehicules = '/conducteur/vehicules';
  static const String conducteurAccidents = '/conducteur/accidents';
  static const String conducteurDeclaration = '/conducteur/declaration';

  // Routes assureur
  static const String assureurHome = '/assureur/home';

  // Routes expert
  static const String expertHome = '/expert/home';

  // Nouvelles routes pour le flux de déclaration
  static const String declarationEntryPoint = '/declaration-entry-point';
  static const String sessionCreation = '/session-creation';
  
  // Routes véhicule
  static const String addVehicule = '/vehicule/add';
  static const String vehiculeDetails = '/vehicule/details';

  static String _getCurrentUserId() {
    final auth = FirebaseAuth.instance;
    return auth.currentUser?.uid ?? '';
  }

  static final Map<String, WidgetBuilder> routes = {
    splash: (context) => const SplashScreen(),
    onboarding: (context) => const OnboardingScreen(),
    languageSelection: (context) => const LanguageSelectionScreen(),
    login: (context) => const LoginScreen(),
    register: (context) => const RegisterScreen(),
    forgotPassword: (context) => const ForgotPasswordScreen(),
    conducteurHome: (context) => const ConducteurHomeScreen(),
    conducteurProfile: (context) => const ConducteurProfileScreen(),
    conducteurAccidents: (context) => const ConducteurAccidentsScreen(),
    assureurHome: (context) => const AssureurHomeScreen(),
    expertHome: (context) => const ExpertHomeScreen(),
    declarationEntryPoint: (context) => const DeclarationEntryPointScreen(),
    sessionCreation: (context) => const SessionCreationScreen(),
    addVehicule: (context) => VehiculeFormScreen(vehicule: null),
    // Note: conducteurVehicules et conducteurDeclaration sont gérés dans generateRoute
    // car ils prennent des arguments.
  };

  static Route<dynamic> generateRoute(RouteSettings settings) {
    final args = settings.arguments;

    switch (settings.name) {
      case conducteurVehicules:
        final Map<String, dynamic>? routeArgs = args as Map<String, dynamic>?;
        final conducteurId = routeArgs?['conducteurId'] as String? ?? _getCurrentUserId();
        final bool selectionMode = routeArgs?['selectionMode'] as bool? ?? false;

        return MaterialPageRoute(
          settings: settings,
          builder: (_) => ConducteurVehiculesScreen(
            conducteurPosition: 'A', // Paramètre requis
            isCollaborative: false, // Valeur par défaut
          ),
        );

      case conducteurDeclaration:
        final Map<String, dynamic> routeArgs = args as Map<String, dynamic>;
        final String sessionId = routeArgs['sessionId'] as String;
        final String conducteurPosition = routeArgs['conducteurPosition'] as String;
        final String? invitationCode = routeArgs['invitationCode'] as String?;
        final VehiculeModel? selectedVehicule = routeArgs['selectedVehicule'] as VehiculeModel?;
        final bool isCollaborative = routeArgs['isCollaborative'] as bool? ?? false;

        return MaterialPageRoute(
          settings: settings,
          builder: (_) => ConducteurDeclarationScreen(
            sessionId: sessionId,
            conducteurPosition: conducteurPosition,
            invitationCode: invitationCode,
            selectedVehicule: selectedVehicule,
            isCollaborative: isCollaborative,
          ),
        );
        
      case vehiculeDetails:
        final VehiculeModel vehicule = args as VehiculeModel;
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => VehiculeFormScreen(vehicule: vehicule),
        );

      default:
        // Gère les routes définies dans la map `routes`
        if (routes.containsKey(settings.name)) {
          return MaterialPageRoute(
            settings: settings,
            builder: routes[settings.name]!,
          );
        }
        // Fallback pour les routes inconnues
        return MaterialPageRoute(
          settings: settings,
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('Page non trouvée')),
            body: Center(
              child: Text('Route inconnue: ${settings.name}'),
            ),
          ),
        );
    }
  }

  static String getHomeRouteByUserType(String userType) {
    switch (userType.toLowerCase()) {
      case 'conducteur':
        return conducteurHome;
      case 'assureur':
        return assureurHome;
      case 'expert':
        return expertHome;
      default:
        return login;
    }
  }

  static String getProfileRouteByUserType(String userType) {
    switch (userType.toLowerCase()) {
      case 'conducteur':
        return conducteurProfile;
      // Ajoutez d'autres types d'utilisateurs si nécessaire
      // case 'assureur': return assureurProfile;
      default:
        return login;
    }
  }
}
