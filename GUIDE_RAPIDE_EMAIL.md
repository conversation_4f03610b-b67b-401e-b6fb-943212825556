# 📧 Guide Rapide - Recevoir de Vrais Emails (5 minutes)

## 🎯 **Problème Actuel**
Vous ne recevez aucun email parce que l'application est en mode démonstration.

## ✅ **Solution en 5 Étapes**

### **1. <PERSON><PERSON>er un compte EmailJS**
- Allez sur [emailjs.com](https://www.emailjs.com)
- Cliquez "Sign Up" 
- Utilisez votre email Gmail

### **2. Connecter Gmail**
- Dans EmailJ<PERSON>, cliquez "Add New Service"
- Choisissez "Gmail"
- Connectez votre compte
- **Copiez le Service ID** (ex: `service_abc123`)

### **3. <PERSON><PERSON>er un template**
- Cliquez "Email Templates" → "Create New Template"
- **Subject:** `{{subject}}`
- **Content:** 
```
{{message}}

Code: {{session_code}}
Lien: {{invitation_link}}
```
- **Co<PERSON>z le Template ID** (ex: `template_xyz789`)

### **4. <PERSON><PERSON><PERSON><PERSON> la clé**
- Allez dans "Account" → "General"
- **Copiez votre Public Key** (ex: `user_def456`)

### **5. Modifier le code**
Dans `lib/core/services/email_service.dart`, remplacez :

```dart
static const String _emailjsServiceId = 'service_abc123'; // VOTRE Service ID
static const String _emailjsTemplateId = 'template_xyz789'; // VOTRE Template ID  
static const String _emailjsPublicKey = 'user_def456'; // VOTRE Public Key
```

## 🚀 **Test**
1. Sauvegardez le fichier
2. Relancez l'app (`flutter run`)
3. Cliquez "Test Email"
4. **Vous recevrez l'email dans votre Gmail !**

## 🔍 **Vérification**
Dans les logs, vous devriez voir :
```
[EmailService] 🚀 Configuration EmailJS détectée - Envoi automatique
[EmailService] ✅ Email envoyé automatiquement avec succès!
```

Au lieu de :
```
[EmailService] ⚠️ EmailJS non configuré - Mode démonstration
```

## 🎉 **Résultat**
- ✅ Emails automatiques dans Gmail
- ✅ 200 emails gratuits par mois
- ✅ Sessions collaboratives fonctionnelles
- ✅ Invitations professionnelles

**Temps total : 5 minutes maximum !**
