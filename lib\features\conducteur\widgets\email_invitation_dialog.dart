import 'package:flutter/material.dart';
import '../../../core/services/email_validation_service.dart';
import '../../../core/utils/session_utils.dart';

class EmailInvitationDialog extends StatefulWidget {
  final int nombreConducteurs;
  final Color currentPositionColor;

  const EmailInvitationDialog({
    Key? key,
    required this.nombreConducteurs,
    required this.currentPositionColor,
  }) : super(key: key);

  @override
  State<EmailInvitationDialog> createState() => _EmailInvitationDialogState();
}

class _EmailInvitationDialogState extends State<EmailInvitationDialog> {
  late List<TextEditingController> _controllers;
  late List<EmailValidationResult?> _validationResults;
  bool _isValidating = false;
  bool _canInvite = false;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.nombreConducteurs - 1, 
      (_) => TextEditingController(),
    );
    _validationResults = List.generate(
      widget.nombreConducteurs - 1, 
      (_) => null,
    );
    
    // Ajouter des listeners pour validation en temps réel
    for (int i = 0; i < _controllers.length; i++) {
      _controllers[i].addListener(() => _validateEmail(i));
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _validateEmail(int index) async {
    final email = _controllers[index].text.trim();
    
    if (email.isEmpty) {
      setState(() {
        _validationResults[index] = null;
        _updateCanInvite();
      });
      return;
    }

    setState(() {
      _isValidating = true;
    });

    try {
      final result = await EmailValidationService.validateEmail(email);
      if (mounted) {
        setState(() {
          _validationResults[index] = result;
          _isValidating = false;
          _updateCanInvite();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _validationResults[index] = EmailValidationResult(
            isValid: false,
            exists: false,
            error: 'Erreur de validation',
          );
          _isValidating = false;
          _updateCanInvite();
        });
      }
    }
  }

  void _updateCanInvite() {
    final nonEmptyEmails = _controllers
        .asMap()
        .entries
        .where((entry) => entry.value.text.trim().isNotEmpty)
        .toList();

    if (nonEmptyEmails.isEmpty) {
      _canInvite = false;
      return;
    }

    _canInvite = nonEmptyEmails.every((entry) {
      final result = _validationResults[entry.key];
      return result != null && result.isValidAndExists;
    });
  }

  List<String> _getValidEmails() {
    List<String> validEmails = [];
    for (int i = 0; i < _controllers.length; i++) {
      final email = _controllers[i].text.trim();
      final result = _validationResults[i];
      
      if (email.isNotEmpty && result != null && result.isValidAndExists) {
        validEmails.add(email);
      }
    }
    return validEmails;
  }

  Widget _buildEmailField(int index) {
    final position = ['B', 'C', 'D', 'E', 'F'][index];
    final color = SessionUtils.getPositionColor(position);
    final result = _validationResults[index];
    final email = _controllers[index].text.trim();

    Color? borderColor;
    Widget? suffixIcon;
    String? helperText;

    if (email.isNotEmpty && result != null) {
      if (result.isValidAndExists) {
        borderColor = Colors.green;
        suffixIcon = const Icon(Icons.check_circle, color: Colors.green);
        helperText = 'Email valide ✓';
      } else {
        borderColor = Colors.red;
        suffixIcon = const Icon(Icons.error, color: Colors.red);
        helperText = result.error ?? 'Email invalide ou inexistant';
      }
    } else if (email.isNotEmpty && _isValidating) {
      suffixIcon = const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
      helperText = 'Vérification en cours...';
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            controller: _controllers[index],
            decoration: InputDecoration(
              labelText: 'Email conducteur $position',
              prefixIcon: Container(
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  position,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              suffixIcon: suffixIcon,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: borderColor ?? Colors.grey,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: borderColor ?? Colors.grey,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: borderColor ?? widget.currentPositionColor,
                  width: 2,
                ),
              ),
            ),
            keyboardType: TextInputType.emailAddress,
            autocorrect: false,
          ),
          if (helperText != null)
            Padding(
              padding: const EdgeInsets.only(top: 4, left: 12),
              child: Text(
                helperText,
                style: TextStyle(
                  fontSize: 12,
                  color: borderColor ?? Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'Inviter les autres conducteurs',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Entrez les adresses email des autres conducteurs impliqués dans l\'accident:',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              ...List.generate(
                widget.nombreConducteurs - 1,
                (index) => _buildEmailField(index),
              ),
              if (_isValidating)
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Validation des emails...',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              const Divider(),
              const Text(
                '💡 Conseils:',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
              ),
              const Text(
                '• Vérifiez que les emails sont corrects\n'
                '• Les conducteurs recevront un lien d\'invitation\n'
                '• Ils pourront rejoindre la session avec le code',
                style: TextStyle(fontSize: 11, color: Colors.grey),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            debugPrint('[EmailInvitationDialog] Dialog annulé');
            Navigator.of(context).pop(<String>[]);
          },
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _canInvite && !_isValidating
              ? () {
                  final validEmails = _getValidEmails();
                  debugPrint('[EmailInvitationDialog] Emails validés: $validEmails');
                  Navigator.of(context).pop(validEmails);
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.currentPositionColor,
            foregroundColor: Colors.white,
          ),
          child: _isValidating
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('Inviter'),
        ),
      ],
    );
  }
}
