import 'package:flutter/material.dart';
import '../../vehicule/models/vehicule_model.dart';

class VehiculeCard extends StatelessWidget {
  final VehiculeModel vehicule;
  final VoidCallback onTap;
  final Color cardColor;
  final Color accentColor;

  const VehiculeCard({
    Key? key,
    required this.vehicule,
    required this.onTap,
    required this.cardColor,
    required this.accentColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      color: cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.directions_car,
                    color: accentColor,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          vehicule.immatriculation,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${vehicule.marque} ${vehicule.modele}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: accentColor,
                    size: 16,
                  ),
                ],
              ),
              if (vehicule.compagnieAssurance.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.security,
                        color: accentColor.withAlpha(180), // Remplacé withOpacity par withAlpha
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        vehicule.compagnieAssurance,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}