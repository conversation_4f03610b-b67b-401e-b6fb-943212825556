buildscript {
    ext.kotlin_version = '2.0.0' 
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
       // classpath 'com.google.gms:google-services:4.3.15'
    }
}

plugins {
    // Add the dependency for the Google services Gradle plugin
    id 'com.google.gms.google-services' version '4.3.15' apply false
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.layout.buildDirectory
}
