import 'package:cloud_firestore/cloud_firestore.dart';

class AssuranceInfoModel {
  final String? id;
  final String societeAssurance;
  final String numeroContrat;
  final String agence;
  final String? agenceId;
  final DateTime? dateDebutValidite;
  final DateTime? dateFinValidite;
  final bool? assuranceValide;
  final String? photoAttestationUrl;
  final String conducteurId; // Lien avec le conducteur
  final DateTime createdAt;
  final DateTime? updatedAt;
  
  AssuranceInfoModel({
    this.id,
    required this.societeAssurance,
    required this.numeroContrat,
    required this.agence,
    this.agenceId,
    this.dateDebutValidite,
    this.dateFinValidite,
    this.assuranceValide,
    this.photoAttestationUrl,
    required this.conducteurId,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'societeAssurance': societeAssurance,
      'numeroContrat': numeroContrat,
      'agence': agence,
      'agenceId': agenceId,
      'dateDebutValidite': dateDebutValidite,
      'dateFinValidite': dateFinValidite,
      'assuranceValide': assuranceValide,
      'photoAttestationUrl': photoAttestationUrl,
      'conducteurId': conducteurId,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'societeAssurance': societeAssurance,
      'numeroContrat': numeroContrat,
      'agence': agence,
      'agenceId': agenceId,
      'dateDebutValidite': dateDebutValidite?.toIso8601String(),
      'dateFinValidite': dateFinValidite?.toIso8601String(),
      'assuranceValide': assuranceValide,
      'photoAttestationUrl': photoAttestationUrl,
      'conducteurId': conducteurId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  static AssuranceInfoModel fromMap(Map<String, dynamic> map) {
    return AssuranceInfoModel(
      id: map['id'],
      societeAssurance: map['societeAssurance'] ?? '',
      numeroContrat: map['numeroContrat'] ?? '',
      agence: map['agence'] ?? '',
      agenceId: map['agenceId'],
      dateDebutValidite: map['dateDebutValidite'] != null 
          ? (map['dateDebutValidite'] as Timestamp).toDate() : null,
      dateFinValidite: map['dateFinValidite'] != null 
          ? (map['dateFinValidite'] as Timestamp).toDate() : null,
      assuranceValide: map['assuranceValide'],
      photoAttestationUrl: map['photoAttestationUrl'],
      conducteurId: map['conducteurId'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: map['updatedAt'] != null 
          ? (map['updatedAt'] as Timestamp).toDate() : null,
    );
  }

  factory AssuranceInfoModel.fromJson(Map<String, dynamic> json) {
    return AssuranceInfoModel(
      id: json['id'],
      societeAssurance: json['societeAssurance'] ?? '',
      numeroContrat: json['numeroContrat'] ?? '',
      agence: json['agence'] ?? '',
      agenceId: json['agenceId'],
      dateDebutValidite: json['dateDebutValidite'] != null 
          ? DateTime.parse(json['dateDebutValidite']) : null,
      dateFinValidite: json['dateFinValidite'] != null 
          ? DateTime.parse(json['dateFinValidite']) : null,
      assuranceValide: json['assuranceValide'],
      photoAttestationUrl: json['photoAttestationUrl'],
      conducteurId: json['conducteurId'] ?? '',
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  AssuranceInfoModel copyWith({
    String? id,
    String? societeAssurance,
    String? numeroContrat,
    String? agence,
    String? agenceId,
    DateTime? dateDebutValidite,
    DateTime? dateFinValidite,
    bool? assuranceValide,
    String? photoAttestationUrl,
    String? conducteurId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AssuranceInfoModel(
      id: id ?? this.id,
      societeAssurance: societeAssurance ?? this.societeAssurance,
      numeroContrat: numeroContrat ?? this.numeroContrat,
      agence: agence ?? this.agence,
      agenceId: agenceId ?? this.agenceId,
      dateDebutValidite: dateDebutValidite ?? this.dateDebutValidite,
      dateFinValidite: dateFinValidite ?? this.dateFinValidite,
      assuranceValide: assuranceValide ?? this.assuranceValide,
      photoAttestationUrl: photoAttestationUrl ?? this.photoAttestationUrl,
      conducteurId: conducteurId ?? this.conducteurId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}